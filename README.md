# <PERSON> – Portfolio (Open Source Edition)

Welcome to the public source of my personal portfolio – [wodniack.dev](https://wodniack.dev), built with [Astro](https://astro.build).

This project performed far beyond my expectations and brought an overwhelming amount of positive feedback.
Thanks for the love. ❤️

I said that if I won a Webby, I’d open-source it. Mission accomplished. 🥳

I’m open-sourcing it so that junior developers, curious minds, and future portfolio crafters can **learn from real-world code**, understand how things are built, and get inspired to build their own.

🚫 **Don't copy/paste it as-is and call it yours.** The community will notice, and you’ll doom yourself to eternal mediocrity.
✅ **Do explore, dissect, and adapt it with purpose.**

---

## 🚀 Getting Started

To run the project locally:

```bash
# 1. Clone this repo
git clone https://github.com/AntoineW/AW-2025-Portfolio.git
cd wodniack-portfolio

# 2. Install dependencies
npm install

# 3. Start the dev server
npm run dev
```

---

## 🧠 Why This Is Open
Curiosity and learning are everything in creative development.
I learned the most by studying real code—Codrops demos, CodePen experiments, random GitHub repos.
This portfolio gave me way more visibility than I expected. Now it’s time to return the love.

---

## 📄 License
This project is licensed under the Creative Commons Attribution-NonCommercial 4.0 International License.

That means:

✅ You can learn from it, fork it, and adapt it for non-commercial purposes.

🚫 You can’t resell it, rebrand it, or use it commercially without permission.

🔗 You must credit the source (me) if you use parts of it.

See LICENSE.md for full details.

---

## 💬 Final Word

This is not a template. It's a real portfolio, shared with intent.
Use it to get better. Not to take shortcuts.
