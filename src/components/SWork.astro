---
import AWork from './AWork.astro'
import dummyVideo from '/src/assets/works/Dummy.mp4'

const videos = await Astro.glob<{ default: any }>('/src/assets/works/*.mp4')

// Yeah, this should probably be in a CMS or content file.
// But it’s my site, and editing it in code is faster than setting up yet another headless something.
// Sometimes "quick and dirty" wins. Don’t @ me.
const info = {
  Pen: {
    title: 'CodePen',
    site: 'https://codepen.io/wodniack',
  },
  Dummy1: {
    title: 'Dummy Project 1',
    site: 'https://wodniack.dev',
  },
  Dummy2: {
    title: 'Dummy Project 2',
    site: 'https://wodniack.dev',
  },
  Dummy3: {
    title: 'Dummy Project 3',
    site: 'https://wodniack.dev',
  },
  Dummy4: {
    title: 'Dummy Project 4',
    site: 'https://wodniack.dev',
  },
  Dummy5: {
    title: 'Dummy Project 5',
    site: 'https://wodniack.dev',
  },
  Dummy6: {
    title: 'Dummy Project 6',
    site: 'https://wodniack.dev',
  },
  Dummy7: {
    title: 'Dummy Project 7',
    site: 'https://wodniack.dev',
  },
  Dummy8: {
    title: 'Dummy Project 8',
    site: 'https://wodniack.dev',
  },
  Dummy9: {
    title: 'Dummy Project 9',
    site: 'https://wodniack.dev',
  },
  Dummy10: {
    title: 'Dummy Project 10',
    site: 'https://wodniack.dev',
  }
}

let projects = []
let pens = []

// I removed the video loops to keep the repo lightweight.
// But this is where I normally fetch and assign the videos for each project in the portfolio.
videos.forEach((video, index) => {
  const src = video.default

  const key = src.split('-')[0].split('/').pop()
  const project = info[key]
  if (!project) return
  const caption = project.title
  const site = project.site

  if (key === 'Pen') {
    pens.push({
      caption,
      site,
      src,
    })
  } else {
    projects.push({
      caption,
      site,
      src,
    })
  }
})

// Create dummy projects with 3 videos each
for (let i = 1; i <= 14; i++) {
  const key = `Dummy${i}`
  const project = info[key]
  if (!project) continue

  for (let j = 0; j < 4; j++) {
    projects.push({
      caption: `${project.title} - Video ${j + 1}`,
      site: project.site,
      src: dummyVideo
    })
  }
}

function shuffle(array) {
  let currentIndex = array.length

  while (currentIndex != 0) {
    let randomIndex = Math.floor(Math.random() * currentIndex)
    currentIndex--
    ;[array[currentIndex], array[randomIndex]] = [
      array[randomIndex],
      array[currentIndex],
    ]
  }
}

shuffle(projects)

// Add pens in between projects regularly
let works = []
let chunkSize = Math.floor(projects.length / pens.length)
let i = 0
let j = 0
while (i < projects.length) {
  works.push(projects[i])
  if (j < pens.length && (i + 1) % chunkSize === 0) {
    works.push(pens[j])
    j++
  }
  i++
}
---

<section id="work" class="s-work" data-intersect>
  <div class="s__outer">
    <div class="s__inner js-container">
      <h2 class="s__title">
        <span class="s__title__inner js-title">
          <span class="s__title__letter js-letter">W</span>
          <span class="s__title__letter js-letter">O</span>
          <span class="s__title__letter js-letter">R</span>
          <span class="s__title__letter js-letter">K</span>
        </span>
      </h2>

      <div class="s__scene js-scene">
        {
          works.map((work, index) => (
            <AWork
              cssClass="s__scene__work s__scene__work--video js-work"
              caption={work.caption}
              site={work.site}
              src={work.src}
              index={String(index).padStart(4, '0')}
              total={String(works.length).padStart(2, '0')}
            />
          ))
        }
      </div><!-- .s__scene -->

      <canvas class="s__canvas js-canvas"></canvas>
    </div><!-- .s__inner -->

    <div class="s__mask-outer">
      <div class="s__mask js-mask">
        <svg class="s__mask__svg js-mask-svg">
          <path class="s__mask__path-inner js-mask-path-inner" d=""></path>
          <path class="s__mask__path-outer js-mask-path-outer" d=""></path>
          <path class="s__mask__path-lines js-mask-path-lines" d=""></path>
        </svg>
      </div><!-- .s__mask -->
    </div><!-- .s__mask-outer -->

    <div class="s__ruler js-ruler"></div>
  </div><!-- .s__outer -->
</section>

<script>
  import Emitter from '../utils/Emitter'
  import Ticker from '../utils/Ticker'

  import { gsap } from 'gsap'
  import { SlowMo } from 'gsap/EasePack'
  import { ScrollTrigger } from 'gsap/ScrollTrigger'
  gsap.registerPlugin(ScrollTrigger, SlowMo)

  class Section {
    el: HTMLElement
    container: HTMLElement
    ruler: HTMLElement
    scene: HTMLElement
    canvas: HTMLCanvasElement
    ctx: CanvasRenderingContext2D
    title: HTMLElement
    videos: NodeListOf<HTMLVideoElement>

    mask: {
      width: number
      height: number
      maxScale: number
      lines: any[]
      el: HTMLElement
      svg: HTMLElement
      pathOuter: HTMLElement
      pathInner: HTMLElement
      pathLines: HTMLElement
    }

    bounding: {
      left: number
      top: number
      width: number
      height: number
    }
    letters: any[]
    works: any[]
    points: any[]

    tl: gsap.core.Timeline
    animationProgress: number
    pointsProgress: number
    last: {
      animationProgress: number
      pointsProgress: number
    }
    scrollProgress: number
    smoothScrollProgress: number
    state: 0
    speed: number
    isPaused: boolean
    loadIsStarted: boolean

    /**
     * Constructor
     */
    constructor() {
      // Elements
      this.el = document.querySelector('.s-work')
      this.container = this.el.querySelector('.js-container')
      this.ruler = this.el.querySelector('.js-ruler')
      this.scene = this.container.querySelector('.js-scene')
      this.canvas = this.container.querySelector('.js-canvas')
      this.ctx = this.canvas.getContext('2d')
      this.title = this.container.querySelector('.js-title')
      this.videos = this.container.querySelectorAll('video')

      // Properties
      this.mask = {
        width: 0,
        height: 0,
        maxScale: 1,
        lines: [],
        el: this.el.querySelector('.js-mask'),
        svg: this.el.querySelector('.js-mask-svg'),
        pathOuter: this.el.querySelector('.js-mask-path-outer'),
        pathInner: this.el.querySelector('.js-mask-path-inner'),
        pathLines: this.el.querySelector('.js-mask-path-lines'),
      }

      this.letters = []
      this.title.querySelectorAll('.js-letter').forEach((_letter) => {
        const letter = {
          el: _letter,
          ghosts: [],
        }

        this.letters.push(letter)
      })

      this.works = []
      this.container.querySelectorAll('.js-work').forEach((_work) => {
        const work = {
          el: _work,
        }

        this.works.push(work)
      })

      this.points = []

      this.scrollProgress = 0
      this.smoothScrollProgress = 0
      this.last = {
        animationProgress: 0,
        pointsProgress: 0,
      }

      this.isPaused = true
      this.loadIsStarted = false

      // Init
      if (document.readyState === 'complete') {
        Ticker.nextTick(this.init, this)
      } else {
        Emitter.once('siteLoaded', this.init, this)
      }
    }

    /**
     * Init
     */
    init() {
      this.setCtxStyle()
      this.setSize()
      this.setMask()
      this.setPoints()
      this.setLetters()
      this.setWorks()
      this.setTimeline()

      this.bindEvents()
    }

    /**
     * Bind events
     */
    bindEvents() {
      Emitter.on('contrastchange', this.setCtxStyle, this)
      Emitter.on('resize', this.onResize, this)

      this.el.addEventListener('intersect', this.onIntersect.bind(this), {
        passive: true,
      })
    }

    /**
     * Resize handler
     */
    onResize(widthChanged) {
      if (widthChanged) {
        this.setCtxStyle()
        this.setSize()
        this.setMask()
        this.setPoints()
        this.setLetters()
        this.setWorks()
        this.setTimeline()
      }
    }

    /**
     * Intersect handler
     */
    onIntersect(e) {
      this.isPaused = !e.detail.isIntersecting

      if (this.isPaused) {
        Emitter.off('tick', this.tick, this)
      } else {
        Emitter.on('tick', this.tick, this)
      }

      if (!this.loadIsStarted) {
        this.loadNextVideo()
        this.loadIsStarted = true
      }
    }

    /**
     * Set canvas style
     */
    setCtxStyle() {
      const color = getComputedStyle(this.el).getPropertyValue(
        '--color-primary'
      )

      Ticker.nextTick(() => {
        this.ctx.strokeStyle = color
      })
    }

    /**
     * Set size
     */
    setSize() {
      this.el.style.setProperty('--height', this.works.length * 50 + 'lvh')

      const bounding = this.container.getBoundingClientRect()

      this.bounding = {
        left: bounding.left,
        top: bounding.top,
        width: window.safeWidth,
        height: window.safeHeight,
      }

      this.canvas.width = this.bounding.width
      this.canvas.height = this.bounding.height

      this.speed = Math.hypot(this.bounding.width, this.bounding.height) * 4
    }

    /**
     * Set mask
     */
    setMask() {
      const { mask } = this

      const width = mask.el.clientWidth
      const height = mask.el.clientHeight

      mask.width = width
      mask.height = height

      mask.svg.style.width = mask.width + 'px'
      mask.svg.style.height = mask.height + 'px'

      const elBounding = this.el.getBoundingClientRect()
      const rulerBounding = this.ruler.getBoundingClientRect()
      const rulerWidth = rulerBounding.width
      const rulerHeight = rulerBounding.height
      const offsetX = rulerBounding.left - elBounding.left
      const offsetY = rulerBounding.top - elBounding.top

      // Shape
      const dOuter = `M -1 0 L ${width + 2} 0 L ${width + 2} ${height} L -1 ${height} Z`

      // Mask outer
      const corners = {
        tl: { x: offsetX, y: offsetY },
        tr: { x: offsetX + rulerWidth, y: offsetY },
        br: { x: offsetX + rulerWidth, y: offsetY + rulerHeight },
        bl: { x: offsetX, y: offsetY + rulerHeight },
      }

      let size = (corners.tr.x - corners.tl.x) / 2

      mask.maxScale = window.safeWidth / size

      let dInner = `M ${corners.tl.x} ${corners.tl.y + size} A ${size} ${size} 0 0 1 ${corners.tr.x} ${corners.tr.y + size} L ${corners.br.x} ${corners.br.y - size} A ${size} ${size} 0 0 1 ${corners.bl.x} ${corners.bl.y - size} Z`
      const linesClip = `${dOuter} ${dInner}`

      mask.pathOuter.setAttribute('d', `${dOuter} ${dInner}`)

      // Mask inner
      const thickness = window.safeWidth > 767 ? 16 : 8
      corners.tl.x += thickness
      corners.tl.y += thickness

      corners.tr.x -= thickness
      corners.tr.y += thickness

      corners.br.x -= thickness
      corners.br.y -= thickness

      corners.bl.x += thickness
      corners.bl.y -= thickness

      size = (corners.tr.x - corners.tl.x) / 2

      dInner = `M ${corners.tl.x} ${corners.tl.y + size} A ${size} ${size} 0 0 1 ${corners.tr.x} ${corners.tr.y + size} L ${corners.br.x} ${corners.br.y - size} A ${size} ${size} 0 0 1 ${corners.bl.x} ${corners.bl.y - size} Z`

      mask.pathInner.setAttribute('d', `${dOuter} ${dInner}`)

      // Lines
      mask.lines = []

      const vLines = window.safeWidth > 767 ? 12 : 8
      const gapX = width / vLines
      const gapY = height * 0.1
      const hLines = Math.ceil(height / gapY)

      for (let i = 1; i < vLines; i++) {
        const x = gapX * i

        mask.lines.push({
          p1: { x, y: 0 },
          p2: { x, y: height },
        })
      }

      for (let i = 0; i < hLines; i++) {
        const y = gapY * i

        mask.lines.push({
          p1: { x: 0, y },
          p2: { x: width, y },
        })
      }

      let dLines = ''

      mask.lines.forEach((line) => {
        dLines += `M ${line.p1.x} ${line.p1.y} L ${line.p2.x} ${line.p2.y} `
      })

      mask.pathLines.setAttribute('d', dLines)
      mask.pathLines.style.clipPath = `path(evenodd, '${linesClip}')`
    }

    /**
     * Set letters
     */
    setLetters() {
      const { letters, scene } = this

      letters.forEach((letter, j) => {
        letter.ghosts.forEach((ghost) => {
          ghost.el.remove()
        })
        letter.ghosts = []

        // Get letter position and size
        const bounding = letter.el.getBoundingClientRect()

        letter.width = bounding.width
        letter.height = bounding.height
        letter.top = bounding.top - this.bounding.top
        letter.left = bounding.left

        letter.freq = 1 + Math.random()

        const multiplier = window.safeWidth > 767 ? 0.75 : 0.5

        // Create ghost letters
        letter.total =
          Math.round((this.bounding.width / letter.width) * multiplier) + 2

        for (let i = 0; i < letter.total; i++) {
          const el = document.createElement('span')
          el.classList.add('s__scene__letter')
          el.classList.add('js-letter')

          el.innerText = letter.el.innerText
          el.dataset.letter = letter.el.innerText

          scene.appendChild(el)

          const ghost = {
            el,
            x: letter.left,
            y: letter.top,
            z: Math.random() * 100,
            i: i - letter.total * 0.5,
            p: (i / letter.total - 0.5) * 2,
            ap: Math.abs(i / letter.total - 0.5) * 2,
            mx: 0,
            my: 0,
          }

          el.style.top = ghost.y + 'px'
          el.style.left = ghost.x + 'px'

          el.style.zIndex = String(j !== 1 && j !== 2 && (j + letters.length + i) % 5 === 0 ? 3 : 1)

          el.style.setProperty('--ix', String(ghost.i))
          el.style.setProperty(
            '--iy',
            String(((j + 1) / (letters.length + 1) - 0.5) * 2)
          )

          el.style.setProperty('--ap', String(ghost.ap))
          el.style.setProperty('--p', String(ghost.p))

          letter.ghosts.push(ghost)
        }
      })
    }

    /**
     * Set works
     */
    setWorks() {
      const { works } = this

      works.forEach((work, i) => {
        const el = work.el

        el.style.setProperty('--size', String(0.5 + Math.random() * 0.5))
        el.style.setProperty(
          '--y',
          String((0.5 + Math.random() * 0.5) * (i % 2 ? -1 : 1))
        )
      })
    }

    /**
     * Set timeline
     */
    setTimeline() {
      const { el, container, works, scene, mask } = this

      const worksEl = works.map((work) => work.el)

      let { tl } = this

      if (tl) {
        tl.kill()
      }

      tl = gsap.timeline({
        scrollTrigger: {
          trigger: el,
          start: 'top 25%',
          end: 'bottom 75%',
          scrub: 1,
        },
        onUpdate: () => {
          scene.style.setProperty('--state', String(this.state))
        },
      })

      tl.fromTo(
        mask.el,
        {
          scale: 1,
        },
        {
          scale: mask.maxScale,
          duration: 0.75,
          ease: 'power4.in',
        },
        0
      )

      tl.fromTo(
        scene,
        {
          scale: 0.75,
        },
        {
          scale: 1,
          duration: 0.75,
          ease: 'power3.in',
        },
        0
      )

      tl.fromTo(
        container,
        {
          clipPath: 'inset(0 1rem)',
        },
        {
          clipPath: 'inset(0 0rem)',
          duration: 0.75,
          ease: 'power3.in',
        },
        0
      )

      tl.fromTo(
        this,
        {
          pointsProgress: 0,
        },
        {
          pointsProgress: 1,
          duration: 1,
          ease: 'power4.inOut',
        },
        0
      )

      tl.fromTo(
        this,
        {
          state: 0,
        },
        {
          state: 1,
          duration: 0.75,
          ease: 'power4.in',
        },
        0
      )

      tl.fromTo(
        worksEl,
        {
          attr: {
            progress: 1,
          },
        },
        {
          attr: {
            progress: -1,
          },
          ease: 'slow(0.15, 0.6)',
          stagger: 0.25,
        },
        0.75
      )

      tl.fromTo(
        this,
        {
          animationProgress: 0,
        },
        {
          animationProgress: 10000,
          duration: tl.totalDuration(),
          ease: 'power1.out',
        },
        0.75
      )

      tl.fromTo(
        this,
        {
          state: 1,
        },
        {
          state: 0,
          duration: 0.75,
          ease: 'power4.inOut',
          immediateRender: false,
        },
        '-=1'
      )

      tl.fromTo(
        mask.el,
        {
          scale: mask.maxScale,
        },
        {
          scale: 1,
          duration: 0.75,
          ease: 'power4.inOut',
          immediateRender: false,
        },
        '-=1'
      )

      tl.fromTo(
        scene,
        {
          scale: 1,
        },
        {
          scale: 0.75,
          duration: 0.75,
          ease: 'power3.inOut',
          immediateRender: false,
        },
        '-=1'
      )

      tl.fromTo(
        container,
        {
          clipPath: 'inset(0 0rem)',
        },
        {
          clipPath: 'inset(0 1rem)',
          duration: 0.75,
          ease: 'power3.inOut',
          immediateRender: false,
        },
        '-=1'
      )

      tl.fromTo(
        this,
        {
          pointsProgress: 1,
        },
        {
          pointsProgress: 0,
          duration: 1,
          ease: 'power4.inOut',
        },
        '-=1'
      )
    }

    /**
     * Load next video
     */
    loadNextVideo() {
      const video = Array.from(this.videos).find(
        (video) => !video.classList.contains('is-loaded')
      )

      if (video) {
        if (video.readyState >= 3) {
          this.videoLoaded(video)
        } else {
          video.addEventListener(
            'canplaythrough',
            () => {
              this.videoLoaded(video)
            },
            { once: true }
          )

          video.setAttribute('src', video.getAttribute('data-src'))
          video.load()
        }
      }
    }

    /**
     * Video loaded
     */
    videoLoaded(video) {
      video.classList.add('is-loaded')
      this.loadNextVideo()
    }

    /**
     * Move letters
     */
    moveLetters() {
      const { speed, letters, animationProgress } = this

      letters.forEach((letter, i) => {
        const letterSpeed = speed * letter.freq
        letter.ghosts.forEach((ghost, index) => {
          let progress =
            (((animationProgress % letterSpeed) / letterSpeed +
              index / letter.total) %
              1) /
              0.7 -
            0.15

          ghost.el.style.setProperty('--progress', String(progress))
        })
      })
    }

    /**
     * Set points
     */
    setPoints() {
      const { bounding } = this

      this.points = []

      const gap = 24
      const cols = Math.ceil((bounding.width * 1.2) / gap)
      const rows = Math.ceil((bounding.height * 1.2) / gap)

      const offsetX = (bounding.width - cols * gap) * 0.5
      const offsetY = (bounding.height - rows * gap) * 0.5

      const hWidth = bounding.width * 0.5
      const hHeight = bounding.height * 0.5

      for (let i = 0; i < cols; i++) {
        for (let j = 0; j < rows; j++) {
          const x = i * gap + offsetX
          const y = j * gap + offsetY

          const dx = hWidth - x
          const dy = hHeight - y

          this.points.push({
            x,
            y,
            dx,
            dy,
            m: Math.random(),
            flowX: 0,
          })
        }
      }
    }

    /**
     * Move points
     */
    movePoints() {
      const { points, animationProgress } = this

      points.forEach((p) => {
        // Flow
        p.flowX = (animationProgress * -0.05) % 24
      })
    }

    /**
     * Draw points
     */
    drawPoints() {
      const { bounding, ctx, points, animationProgress, pointsProgress, last } =
        this

      const rAnimationProgress = Math.round(animationProgress * 100) / 100
      const rPointsProgress = Math.round(pointsProgress * 100) / 100

      if (
        rPointsProgress === last.pointsProgress &&
        rAnimationProgress === last.animationProgress
      )
        return

      ctx.clearRect(0, 0, bounding.width, bounding.height)

      ctx.beginPath()

      points.forEach((point) => {
        const x = point.x + point.dx * (1 - pointsProgress) * 0.2 + point.flowX
        const y = point.y + point.dy * (1 - pointsProgress) * 0.2

        ctx.rect(x, y, 0.5, 0.5)
      })

      ctx.stroke()

      last.pointsProgress = rPointsProgress
      last.animationProgress = rAnimationProgress
    }

    /**
     * Tick
     */
    tick() {
      // Calculate scroll progress
      this.scrollProgress =
        Math.max(
          Math.min(1, ScrollTrigger.positionInViewport(this.el, 'top')),
          0
        ) *
          -1 +
        (1 -
          Math.max(
            Math.min(1, ScrollTrigger.positionInViewport(this.el, 'bottom')),
            0
          ))
      this.smoothScrollProgress +=
        (this.scrollProgress - this.smoothScrollProgress) * 0.1

      this.el.style.setProperty(
        '--scroll-progress',
        String(this.scrollProgress)
      )

      // Movement
      this.movePoints()
      this.moveLetters()

      // Draw
      this.drawPoints()
    }
  }

  new Section()
</script>

<style lang="scss">
  .s-work {
    --height: 100lvh;

    position: relative;
    z-index: 2;

    height: var(--height);

    background: color(primary);

    &:before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;

      width: 100%;
      height: 1px;

      background: color(secondary);

      content: '';
    }

    .s__mask-outer {
      position: sticky;
      top: 0;
      left: 0;

      width: 100%;
      height: 100lvh;

      overflow: hidden;
      pointer-events: none;
    }

    .s__mask {
      position: absolute;
      top: 0;
      left: 0;

      width: 100%;
      height: 100%;

      will-change: scale, transform;

      &__svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;

        // transform: translate3d(0, 0, 0);
      }

      &__path-outer {
        fill: color(primary);
        fill-rule: evenodd;
        stroke: color(secondary);
        stroke-width: 1px;
      }

      &__path-inner {
        fill: color(primary);
        fill-rule: evenodd;
        stroke: color(secondary);
        stroke-width: 1px;

        transform: translate3d(0, calc(var(--scroll-progress) * 48px), 0);

        will-change: transform;
      }

      &__path-lines {
        fill: none;
        stroke: color(secondary);
        stroke-width: 1px;
      }
    }

    .s__outer {
      position: relative;

      height: var(--height);

      clip-path: inset(0 -1rem);
    }

    .s__inner {
      @include flex(column, center, center);

      position: fixed;
      top: 0;
      left: 0;

      width: 100%;
      height: 100%;

      background: color(secondary);
      transform: translate3d(0, calc(var(--scroll-progress) * -15%), 0);

      color: color(primary);

      will-change: clip-path, transform;

      @include mq(phone) {
        transform: none;
      }
    }

    .s__ruler {
      --width: min(16.6667%, 19.625rem);

      position: absolute;
      top: 10lvh;
      left: calc(50% - var(--width) / 2);

      width: var(--width);
      height: 80lvh;

      // background: #0f03;
      pointer-events: none;

      @include mq(tablet) {
        --width: 33.3333%;
      }

      @include mq(phone) {
        --width: 50%;
      }
    }

    .s__title,
    .s__scene {
      font: 700 min(18.75rem, 25lvh) / 1 font-family(bigger);
      text-align: center;
      text-transform: uppercase;
    }

    .s__title__inner,
    :global(.s__scene__letter) {
      line-height: 0.85;
    }

    .s__title {
      width: 0.7em;

      opacity: 0;
      word-break: break-all;

      &__inner {
        @include flex(column, center, center);

        margin: 0.075em 0 -0.125em;
      }
    }

    .s__scene {
      --shadow: #{color(shadow)};
      --progress: 0.5;
      --state: 0;

      position: absolute;
      top: 0;
      left: 0;
      z-index: 2;

      width: 100%;
      height: 100%;

      perspective: 40rem;

      will-change: transform;

      :global(.s__scene__letter) {
        --head: calc((var(--progress) - 0.5) * -2);
        --ahead: calc(var(--head) * var(--head));

        position: absolute;
        top: 0;
        left: 0;

        display: block;

        transform: rotateY(calc(var(--head) * -10deg * var(--state)))
          translate3d(
            calc(var(--head) * 50vw * var(--state)),
            calc(var(--iy) * 50% * var(--ahead) * var(--state)),
            0
          );

        pointer-events: none;
        will-change: transform;

        &:before {
          position: absolute;
          top: 0;
          left: 0;
          z-index: -1;

          color: color(shadow);

          opacity: min(calc( var(--state) * 2 ), 1);
          transform: scale(
              1.05, 1.02
            )
            translate3d(
              calc(var(--head) * 0.1rem * var(--state) * var(--state)),
              0,
              0
            );
          transform-origin: calc(50% - var(--head) * 50%) -50%;

          will-change: opacity, transform;

          content: attr(data-letter);
        }
      }

      :global(.s__scene__work) {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 2;

        transform: rotateY(calc(var(--progress) * -20deg))
          translate3d(
            calc(var(--progress) * (50vw + 100%) - 50%),
            calc(var(--y) * 50% - 50%),
            calc(var(--progress) * var(--progress) * -5rem)
          )
          scale(var(--size));

        will-change: transform;

        @include mq(tablet) {
          transform: rotateY(calc(var(--progress) * -20deg))
            translate3d(
              calc(var(--progress) * (50vw + 100%) - 50%),
              calc(var(--y) * 50% - 50%),
              calc(var(--progress) * var(--progress) * -5rem)
            );
        }

        @include mq(phone) {
          transform: rotateY(calc(var(--progress) * -20deg))
            translate3d(
              calc(var(--progress) * (50vw + 100%) - 50%),
              calc(var(--y) * 100% - 50%),
              calc(var(--progress) * var(--progress) * -5rem)
            );
        }
      }
    }

    .s__canvas {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 1;

      width: 100%;
      height: 100%;

      transform: translate3d(0, calc(var(--scroll-progress) * -5%), 0);

      @include mq(phone) {
        transform: none;
      }
    }
  }
</style>
