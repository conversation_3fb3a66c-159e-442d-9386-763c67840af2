---
const {
  class: className,
  style = 'primary',
  strings = ['Do', 'Things', 'Your', 'Way'],
} = Astro.props

const binaries = strings.map((string) => {
  return string
    .split('')
    .map((char) => char.charCodeAt(0).toString(2))
    .join(' ')
})
---

<a-separator class:list={['style--' + style, className]} data-intersect>
  <span class="a__triangle"></span>

  <span class="a__binaries">
    {
      binaries.map((binary) => (
        <>
          <span class="a__code js-code">
            {binary.split('').map((char) => (
              <span
                class:list={[
                  'a__char',
                  'a__char--' + (char === ' ' ? 'blank' : char),
                  'js-char',
                ]}
              >
                {char}
              </span>
            ))}
          </span>
          <span class="a__stripes" />
        </>
      ))
    }
  </span>

  <span class="a__triangle"></span>
</a-separator>

<script>
  import Emitter from '../utils/Emitter'

  class ASeparator extends HTMLElement {
    chars: NodeListOf<HTMLElement>
    codes: NodeListOf<HTMLElement>

    isPaused: boolean

    /**
     * Init
     */
    connectedCallback() {
      // Elements
      this.codes = this.querySelectorAll('.js-code')
      this.chars = this.querySelectorAll('.js-char')

      // Properties
      this.isPaused = true

      // Init
      this.bindEvents()
    }

    /**
     * Bind events
     */
    bindEvents() {
      this.addEventListener('intersect', this.onIntersect.bind(this), {
        passive: true,
      })
    }

    /**
     * Intersect handler
     */
    onIntersect(e) {
      this.isPaused = !e.detail.isIntersecting

      if (this.isPaused) {
        Emitter.off('tick', this.tick, this)
      } else {
        Emitter.on('tick', this.tick, this)
      }
    }

    /**
     * Tick
     */
    tick() {
      this.chars.forEach((char) => {
        if (char.classList.contains('a__char--blank') || Math.random() > 0.1)
          return

        char.classList.remove('a__char--0')
        char.classList.remove('a__char--1')
        char.classList.add('a__char--' + (Math.random() > 0.5 ? '0' : '1'))
      })
    }
  }

  customElements.define('a-separator', ASeparator)
</script>

<style lang="scss">
  a-separator {
    @include flex(row, center, space-between);

    position: relative;

    padding: 0 1rem;
    width: 100%;
    height: 2.25rem;

    border-top: 1px solid color(secondary);
    border-bottom: 1px solid color(secondary);

    font: 400 8px / 16px font-family(fraktion);

    .a__triangle {
      position: absolute;
      top: 50%;

      border-top: 2px solid transparent;
      border-bottom: 2px solid transparent;
      transform: translate3d(0, -40%, 0);

      &:first-child {
        left: 1rem;

        border-left: 4px solid color(secondary);
      }

      &:last-child {
        right: 1rem;

        border-right: 4px solid color(secondary);
      }
    }

    .a__binaries {
      @include flex(row, center, space-between);

      padding: 0 0.7rem;
    }

    .a__code {
      @include flex(row, flex-start, flex-start);

      flex-grow: 0;
      flex-shrink: 0;

      @include mq(tablet-sm) {
        &:nth-child(4n + 3) {
          display: none;
        }
      }

      @include mq(phone) {
        &:nth-child(1n) {
          display: flex;
        }

        &:nth-child(6n + 3),
        &:nth-child(6n + 5) {
          display: none;
        }
      }
    }

    .a__char {
      position: relative;

      display: block;
      overflow: hidden;

      color: transparent;

      &:before {
        position: absolute;
        top: 0;
        left: 0;

        width: 100%;
        height: 2em;

        color: color(secondary);
        word-break: break-all;

        will-change: translate;

        content: '01';
      }

      &--1 {
        &:before {
          translate: 0 -100%;
        }
      }

      &--blank {
        &:before {
          content: none;
        }
      }
    }

    .a__stripes {
      margin: 0 8px;
      height: 8px;

      overflow: hidden;

      text-shadow: 0 0 1px color(secondary);
      // -webkit-text-stroke: 1px color(secondary);

      line-height: 8px;

      @include mq(tablet-sm) {
        &:nth-child(4n + 4) {
          display: none;
        }
      }

      @include mq(phone) {
        &:nth-child(1n) {
          display: block;
        }

        &:nth-child(6n + 4),
        &:nth-child(6n + 6) {
          display: none;
        }
      }

      &:before {
        content: '/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////';
        word-break: break-word;
      }

      &:last-child {
        display: none;
      }
    }

    .style--secondary {
      background: color(secondary);

      color: color(primary);

      .a__triangle {
        &:first-child {
          border-left-color: color(primary);
        }

        &:last-child {
          border-right-color: color(primary);
        }
      }

      .a__stripes {
        text-shadow: 0 0 1px color(primary);
      }
    }
  }
</style>
