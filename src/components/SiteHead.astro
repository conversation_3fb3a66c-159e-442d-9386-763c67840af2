---
const menu = [
  {
    id: 'about',
    text: 'About',
  },
  {
    id: 'work',
    text: 'Work',
  },
  {
    id: 'contact',
    text: 'Contact',
  },
]
---

<header class="site-head" data-intersect>
  <div class="site-head__container">
    <div class="sb-logo">
      <a href="/">
        <svg
          class="js-logo"
          width="280"
          height="280"
          viewBox="0 0 280 280"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M240.245 0v263.2h-19.894V0h-39.756v263.2h-19.861V0h-39.755v280H280V0h-39.755Z"
            fill="#160000"
          >
          </path>
          <path d="M0 0v280h39.755V16.8H59.65V280h39.756V0H0Z" fill="#160000">
          </path>
        </svg>

        <span class="u-sr-only"><PERSON></span>
      </a>
    </div><!-- .sb-logo -->

    <div class="sb-console" role="presentation">
      <div class="sb-console__inner js-console">
      </div><!-- .sb-console__inner -->
    </div><!-- .sb-console -->

    <nav class="sb-menu">
      <ul class="sb__list">
        {
          menu.map((item) => (
            <li class="sb__item js-menu-item">
              <a href={'#' + item.id} class="js-menu-link">
                <span class="sb__text">{item.text}</span>
              </a>
            </li>
          ))
        }
      </ul>
    </nav><!-- .sb-menu -->

    <ul class="sb-socials">
      <li class="sb__item">
        <a href="https://codepen.io/wodniack" target="_blank" rel="noopener">
          <span
            class="sb__icon sb__icon--codepen"
            style="--path: path('M19.3184 6.01751L10.4517 0.105976C10.3477 0.0365379 10.2254 -0.000518799 10.1002 -0.000518799C9.97513 -0.000518799 9.85282 0.0365379 9.74874 0.105976L0.882076 6.01751C0.795245 6.07529 0.724029 6.15362 0.674752 6.24555C0.625475 6.33747 0.599663 6.44014 0.599609 6.54444V12.456C0.599609 12.6675 0.706009 12.8651 0.882076 12.9829L9.74874 18.8944C9.85269 18.9636 9.97476 19.0005 10.0996 19.0005C10.2245 19.0005 10.3465 18.9636 10.4505 18.8944L19.3171 12.9829C19.404 12.9251 19.4752 12.8468 19.5245 12.7549C19.5737 12.6629 19.5996 12.5603 19.5996 12.456V6.54444C19.5996 6.44014 19.5737 6.33747 19.5245 6.24555C19.4752 6.15362 19.404 6.07529 19.3171 6.01751H19.3184ZM10.1009 11.6934L6.80881 9.49958L10.1009 7.30571L13.3929 9.49958L10.1009 11.6934ZM10.7342 6.20498V1.81598L17.8263 6.54318L14.5342 8.73704L10.7342 6.20371V6.20498ZM9.46754 6.20498L5.66754 8.73831L2.37548 6.54444L9.46754 1.81724V6.20498ZM4.52628 9.49958L1.86754 11.2716V7.72751L4.52628 9.49958ZM5.66754 10.2608L9.46754 12.7942V17.1832L2.37548 12.456L5.66754 10.2608ZM10.7342 12.7942L14.5342 10.2608L17.8263 12.4547L10.7342 17.1819V12.7942ZM15.6755 9.49958L18.3342 7.72751V11.2716L15.6755 9.49958Z');"
          >
          </span>

          <span class="u-sr-only">Follow me on CodePen</span>
        </a>
      </li>

      <li class="sb__item">
        <a
          href="https://www.linkedin.com/in/wodniack/"
          target="_blank"
          rel="noopener"
        >
          <span
            class="sb__icon sb__icon--linkedin"
            style="--path: path('M1.13025 14.9839H3.93671V4.6H1.13025V14.9839ZM2.53348 0.0161285C1.598 0.0161285 0.849609 0.764516 0.849609 1.7C0.849609 2.63548 1.598 3.38387 2.53348 3.38387C3.46896 3.38387 4.21735 2.63548 4.21735 1.7C4.21735 0.764516 3.46896 0.0161285 2.53348 0.0161285ZM8.70767 6.19032V4.6H5.90122V14.9839H8.70767V9.65161C8.70767 6.65806 12.5432 6.47097 12.5432 9.65161V14.9839H15.3496V8.62258C15.3496 3.57097 10.0174 3.75806 8.70767 6.19032Z');"
          >
          </span>

          <span class="u-sr-only">Follow me on LinkedIn</span>
        </a>
      </li>
    </ul><!-- .sb-socials -->

    <button class="sb-contrast js-contrast" type="button">
      <span
        class="sb__icon"
        style="--path: path('M10.0996 20C8.71628 20 7.41628 19.7373 6.19961 19.212C4.98294 18.6867 3.92461 17.9743 3.02461 17.075C2.12461 16.1757 1.41228 15.1173 0.887611 13.9C0.362944 12.6827 0.100277 11.3827 0.0996106 10C0.098944 8.61733 0.361611 7.31733 0.887611 6.1C1.41361 4.88267 2.12594 3.82433 3.02461 2.925C3.92328 2.02567 4.98161 1.31333 6.19961 0.788C7.41761 0.262667 8.71761 0 10.0996 0C11.4816 0 12.7816 0.262667 13.9996 0.788C15.2176 1.31333 16.2759 2.02567 17.1746 2.925C18.0733 3.82433 18.7859 4.88267 19.3126 6.1C19.8393 7.31733 20.1016 8.61733 20.0996 10C20.0976 11.3827 19.8349 12.6827 19.3116 13.9C18.7883 15.1173 18.0759 16.1757 17.1746 17.075C16.2733 17.9743 15.2149 18.687 13.9996 19.213C12.7843 19.739 11.4843 20.0013 10.0996 20ZM11.0996 17.925C13.0829 17.675 14.7456 16.804 16.0876 15.312C17.4296 13.82 18.1003 12.0493 18.0996 10C18.0989 7.95067 17.4279 6.18 16.0866 4.688C14.7453 3.196 13.0829 2.325 11.0996 2.075V17.925Z');"
      >
      </span>

      <span class="u-sr-only">Change contrast</span>
    </button><!-- .sb-contrast -->

    <aside class="sb-availability">
      <p>
        <span class="sb__line">
          <span class="sb__text">Coding globally from France.</span>
        </span>

        <span class="sb__line">
          <span class="sb__text">Available for freelance work →</span>

          <a href="mailto:<EMAIL>" class="sb__link">Hire me</a>
        </span>
      </p>
    </aside><!-- .sb-availability -->

    <a
      href="mailto:<EMAIL>"
      class="sb-qr-code js-qr-code"
      title="Contact me!"
    >
      <img src="/images/qr-code.svg" alt="QR Code" width="72" height="72" />
    </a><!-- .sb-qr-code -->
  </div><!-- .site-head__container -->
</header>

<script>
  import Emitter from '../utils/Emitter'
  import Ticker from '../utils/Ticker'

  import { gsap } from 'gsap'

  class Section {
    el: HTMLElement
    contrastButton: HTMLElement
    console: HTMLElement
    contrastMask: HTMLElement
    links: NodeListOf<HTMLElement>

    messages: string[]
    message: string
    messageLineBreak: boolean
    lastMessage: string
    lastTypeTime: number
    writeDelay: number
    canWrite: boolean
    isPaused: boolean

    /**
     * Constructor
     */
    constructor() {
      // Elements
      this.el = document.querySelector('.site-head')
      this.contrastButton = document.querySelector('.js-contrast')
      this.console = document.querySelector('.js-console')
      this.contrastMask = document.querySelector('.js-contrast-mask')
      this.links = this.el.querySelectorAll('.js-menu-link')

      // Properties
      this.messages = [
        'Preparing for inevitable debugging',
        'Compiling designer dreams…into developer nightmares',
        'Please wait while I overthink this',
        'Optimizing… but nothing’s perfect',
        'Configuring the next minor inconvenience',
        'Fetching assets… contemplating the futility of it all',
        'Re-routing your expectations… expect delays',
        'Trying to animate enthusiasm… it’s not going well',
        'Stuck in an infinite loop',
        'Loading… still pointless',
        'Simulating progress… sort of',
        'This will probably break soon',
        'Simulating something useful',
        'Progress bar full of lies',
        'Finding meaning in the code',
        'Calculating failure probabilities',
        'Please wait… indefinitely',
        'Loading… almost there!',
        'Animating pixels with love',
        'Integrating magic and code',
        'Optimizing creativity… stand by',
        'Design and code handshake',
        'Fetching creativity… almost done!',
        'Preparing awesomeness',
        'Simulating brilliance… probably',
        'Everything is under control',
        'Loading coolness… almost ready',
        'Calibrating designer dreams',
        'Fusing design and animation',
        'Running creativity protocols',
        'Crafting magic… please wait',
        'Making things pretty… hold on',
        'Loading… this might take a bit',
        'Animating pixels… somewhat precisely',
        'Integrating code and reality',
        'Halfway done… maybe',
        'Optimizing… cautiously hopeful',
        'Design meets code… fingers crossed',
        'Fetching some interesting stuff',
        'Preparing… slowly but surely',
        'Aligning pixels… carefully',
        'Calibrating… what exactly? Good question',
        'Waiting… patience is key',
        'Simulating… something, probably',
        'Loading… feel free to blink',
        'Running some clever algorithms',
        'Almost there… give or take',
        'Integrating… like a pro',
        'Crafting… without breaking anything',
        'Adjusting fonts… nearly invisible',
        'Piecing it together… stay tuned',
        'Loading… nothing to see yet',
        'Running final checks… hopefully',
        'Almost ready… trust me',
        'Building… it’s getting there',
        'Loading… but why rush?',
        'Please wait… or don’t, whatever',
        'Initializing… prepare for bugs',
        'Optimizing… but who cares?',
        'Deploying… probably not broken',
        'Making things work… hopefully',
        'Running… but not too fast',
        'Testing patience… stay calm',
        'Initializing… no promises',
        'Loading… but who’s counting?',
        'Loading… could be worse',
      ]
      this.message = ''
      this.messageLineBreak = false
      this.lastMessage = ''
      this.lastTypeTime = 0
      this.writeDelay = 0
      this.canWrite = false
      this.isPaused = true

      // Init
      Ticker.nextTick(this.init, this)
    }

    /**
     * Init
     */
    init() {
      this.bindEvents()
    }

    /**
     * Bind events
     */
    bindEvents() {
      this.contrastButton.addEventListener(
        'click',
        this.toggleContrast.bind(this),
        { passive: true }
      )

      this.links.forEach((link) => {
        link.addEventListener('click', this.moveToSection.bind(this))
      })

      document.addEventListener('intro', this.intro.bind(this), { once: true })

      this.el.addEventListener('intersect', this.onIntersect.bind(this), {
        passive: true,
      })
    }

    /**
     * Intersect handler
     */
    onIntersect(e) {
      this.isPaused = !e.detail.isIntersecting

      if (this.isPaused) {
        Emitter.off('tick', this.updateConsole, this)
      } else {
        Emitter.on('tick', this.updateConsole, this)
      }
    }

    /**
     * Intro
     */
    intro() {
      const logo = this.el.querySelector('.js-logo')
      const menuItems = this.el.querySelectorAll('.js-menu-item')
      const qrCode = this.el.querySelector('.js-qr-code')

      const items = [logo, ...menuItems]

      const tl = gsap.timeline()

      tl.set(this.el, {
        opacity: 1,
      })

      tl.from(
        this.el,
        {
          y: '-100%',
          duration: 1.5,
          ease: 'expo.inOut',
        },
        1
      )

      tl.from(
        items,
        {
          y: '-100%',
          duration: 1.5,
          ease: 'expo.out',
          stagger: 0.1,
        },
        1.5
      )

      tl.fromTo(
        qrCode,
        {
          '--bg-p': '0%',
        },
        {
          '--bg-p': '100%',
          duration: 1.5,
          ease: 'expo.out',
        },
        1.75
      )

      tl.call(
        () => {
          this.canWrite = true
        },
        null,
        1.5
      )
    }

    /**
     * Toggle contrast
     */
    toggleContrast() {
      let fromX = '0'
      let toX = '-100%'

      if (document.documentElement.classList.contains('theme-contrasted')) {
        fromX = '-100%'
        toX = '0'
      }

      gsap.fromTo(
        this.contrastMask,
        {
          x: fromX,
        },
        {
          x: toX,
          duration: 1,
          ease: 'expo.inOut',
          onComplete: () => {
            this.contrastMask.style.transform = ''

            if (toX === '0') {
              document.documentElement.classList.remove('theme-contrasted')
            } else {
              document.documentElement.classList.add('theme-contrasted')
            }

            Emitter.emit(
              'contrastchange',
              document.documentElement.classList.contains('theme-contrasted')
                ? 'contrasted'
                : 'default'
            )
          },
        }
      )

      if (toX !== '0') {
        document.documentElement.classList.add('theme-contrasted')
      }
    }

    /**
     * Move to section
     */
    moveToSection(e) {
      e.preventDefault()

      const id = e.currentTarget.getAttribute('href')

      window.lenis.scrollTo(id, {
        duration: 1.5,
        easing: (t) =>
          t < 0.5 ? 16 * t * t * t * t * t : 1 + 16 * --t * t * t * t * t,
      })
    }

    /**
     * Update console
     */
    updateConsole(time) {
      if (!this.canWrite || time - this.lastTypeTime < this.writeDelay) {
        return
      }

      if (this.message === '') {
        this.message = this.getRandomMessage()
        this.writeDelay = 2000
      } else {
        if (this.message === this.lastMessage || this.messageLineBreak) {
          this.console.textContent += '\n'
        }

        const char = this.message.charAt(0)
        this.message = this.message.substring(1)

        if (char === ',') {
          this.writeDelay = 100
        } else if (char === ' ') {
          this.writeDelay = 100
        } else if (char === '') {
          this.writeDelay = 200
        } else if (char === '…') {
          this.writeDelay = 400
        } else if (char === '.') {
          this.writeDelay = 400
        } else {
          this.writeDelay = 20
        }

        this.console.textContent += char

        if (char === '…') {
          this.messageLineBreak = true
        } else {
          this.messageLineBreak = false
        }
      }

      this.console.textContent = this.console.textContent
        .split('\n')
        .slice(-5)
        .join('\n')

      this.lastTypeTime = time
    }

    /**
     * Get random message
     */
    getRandomMessage() {
      let message =
        this.messages[Math.floor(Math.random() * this.messages.length)]

      if (message === this.lastMessage) {
        message = this.getRandomMessage()
      }

      this.lastMessage = message

      return message
    }
  }

  new Section()
</script>

<style lang="scss">
  .site-head {
    position: relative;
    z-index: 5;

    width: 100%;

    border-bottom: 1px solid color(secondary);
    opacity: 0;

    &__container {
      @include flex(row, stretch, space-between);

      width: 100%;
      height: 6rem;

      @include mq(phone) {
        height: 4rem;
      }
    }

    .sb-logo {
      padding: 1.5rem;
      width: 6rem;
      height: 6rem;

      border-right: 1px solid color(secondary);

      @include mq(tablet) {
        order: 1;
      }

      @include mq(phone) {
        padding: 1rem;
        width: 4rem;
        height: 4rem;
      }

      svg {
        width: 3rem;
        height: 3rem;

        @include mq(phone) {
          width: 1.875rem;
          height: 1.875rem;
        }
      }
    }

    .sb-console {
      @include flex(column, flex-start, flex-end);

      margin: 0 auto 0 0;
      padding: 1.5rem;

      font: 400 8px / 1.4 font-family(fraktion);
      text-transform: uppercase;
      white-space: pre-line;

      @include mq(tablet) {
        order: 2;
      }

      @include mq(tablet-sm) {
        display: none;
      }

      @keyframes site-head-caret {
        0%,
        100% {
          opacity: 1;
        }

        50% {
          opacity: 0;
        }
      }

      &__inner {
        @include flex(column, flex-start, flex-end);

        height: 5.6em;

        overflow: hidden;
      }
    }

    .sb-menu {
      border-left: 1px solid color(secondary);

      @include mq(desktop-xs) {
        padding: 0 1rem;
      }

      @include mq(tablet) {
        display: none;
      }

      .sb__list {
        @include flex(row, center, flex-start);

        margin: 0;
        padding: 0;
        height: 100%;

        list-style: none;
      }

      .sb__item {
        margin: 0;
        padding: 0;
      }

      .sb__text {
        position: relative;

        display: inline-block;

        &:before {
          position: absolute;
          top: calc(50% - 3px);
          left: -13px;

          border-top: 3px solid transparent;
          border-left: 6px solid currentcolor;
          border-bottom: 3px solid transparent;
          opacity: 0;

          content: '';
        }
      }

      a {
        display: block;
        padding: 2rem 3rem;

        color: color(secondary);
        font: 400 14px / 1 font-family(fraktion);
        letter-spacing: 0.05em;
        text-decoration: none;
        text-transform: uppercase;

        @include mq(desktop-sm) {
          padding: 2rem;
        }

        @include mq(desktop-xs) {
          padding: 2rem 1rem;
        }

        @keyframes blink-in {
          0%,
          30%,
          60% {
            opacity: 0;
          }

          15%,
          45%,
          75%,
          100% {
            opacity: 1;
          }
        }

        &:hover {
          text-shadow: 0 0 1px currentcolor;

          .sb__text:before {
            animation: blink-in 0.3s ease(in-out-expo) forwards;
          }
        }
      }
    }

    .sb-socials {
      margin: 0;
      padding: 0;

      background: linear-gradient(
        0deg,
        transparent calc(50% - 1px),
        color(secondary) calc(50% - 1px),
        color(secondary) 50%,
        transparent 50%
      );
      border-left: 1px solid color(secondary);
      list-style: none;

      @include mq(tablet) {
        order: 4;
      }

      @include mq(phone) {
        @include flex(row, center, center);

        order: 3;

        margin-left: auto;

        background: none;
        border-left: 0;
      }

      .sb__item {
        margin: 0;
        padding: 0;

        @include mq(phone) {
          @include flex(column, center, center);

          height: 100%;

          border-left: 1px solid color(secondary);
        }
      }

      .sb__icon {
        background: color(secondary);

        clip-path: var(--path);

        &:before {
          position: absolute;
          top: 0;
          left: 0;

          display: block;
          width: 100%;
          height: 100%;

          background: color(primary);
          scale: 0 1;
          transform-origin: 0 50%;

          content: '';
        }

        &--codepen {
          width: 20px;
          height: 19px;
        }

        &--linkedin {
          width: 16px;
          height: 15px;
        }
      }

      a {
        @include flex(row, center, center);

        position: relative;

        width: 48px;
        height: 48px;

        @include mq(phone) {
          height: 100%;
        }

        &:before {
          position: absolute;
          top: 0;
          left: 0;

          width: 100%;
          height: 100%;

          background: color(secondary);
          scale: 0 1;
          transform-origin: 0 50%;

          content: '';
        }

        &:hover {
          &:before {
            scale: 1 1;
          }

          .sb__icon {
            &:before {
              scale: 1 1;
            }
          }
        }
      }

      a:before,
      .sb__icon:before {
        transition: scale 0.3s ease(in-out-expo);
      }
    }

    .sb-contrast {
      @include flex(row, center, center);

      position: relative;

      width: 48px;
      height: 96px;

      appearance: none;
      background: none;
      border: 0;
      border-left: 1px solid color(secondary);
      border-radius: 0;
      cursor: pointer;

      @include mq(tablet) {
        order: 5;
      }

      @include mq(phone) {
        height: 100%;
      }

      &:before {
        position: absolute;
        top: 0;
        left: 0;

        width: 100%;
        height: 100%;

        background: color(secondary);
        scale: 0 1;
        transform-origin: 0 50%;

        content: '';
      }

      .sb__icon {
        width: 20px;
        height: 20px;

        background: color(secondary);
        clip-path: var(--path);

        &:before {
          position: absolute;
          top: 0;
          left: 0;

          display: block;
          width: 100%;
          height: 100%;

          background: color(primary);
          scale: 0 1;
          transform-origin: 0 50%;

          content: '';
        }
      }

      &:hover {
        &:before {
          scale: 1 1;
        }

        .sb__icon {
          &:before {
            scale: 1 1;
          }
        }
      }

      &:before,
      .sb__icon:before {
        transition: scale 0.3s ease(in-out-expo);
      }
    }

    .sb-availability {
      background: linear-gradient(
        0deg,
        transparent calc(50% - 1px),
        color(secondary) calc(50% - 1px),
        color(secondary) 50%,
        transparent 50%
      );
      border-left: 1px solid color(secondary);

      font: 200 1.25rem / 48px font-family(editorial);

      @include mq(tablet) {
        order: 3;
      }

      @include mq(tablet-sm) {
        flex-grow: 1;

        border-left: 0;
      }

      @include mq(phone) {
        @include flex(column, center, center);

        flex-grow: 0;

        background: none;
        border-left: 1px solid color(secondary);
        order: 3;
      }

      p {
        margin: 0;
      }

      .sb__line {
        display: block;
        padding: 1px 1.25rem 0;
      }

      .sb__text {
        @include mq(phone) {
          display: none;
        }
      }

      a {
        position: relative;

        display: inline-block;

        color: color(secondary);
        font-weight: 400;
        text-decoration: none;

        &:before {
          position: absolute;
          bottom: calc(50% - 0.5em);
          left: 0;

          width: 100%;
          height: 1px;

          background: currentcolor;
          scale: 0 1;
          transform-origin: 100% 50%;

          transition: scale 0.3s ease(in-out-expo);

          content: '';
        }

        &:hover {
          &:before {
            scale: 1 1;
            transform-origin: 0 50%;
          }
        }
      }
    }

    .sb-qr-code {
      --bg-p: 0%;

      position: relative;

      display: block;
      padding: 0.75rem;

      border-left: 1px solid color(secondary);

      @include mq(desktop-sm) {
        display: none;
      }

      &:before {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        bottom: 0.75rem;
        left: 0.75rem;

        background: linear-gradient(
            180deg,
            transparent,
            transparent var(--bg-p),
            color(secondary) var(--bg-p),
            color(secondary)
          )
          top / 100% 12.5% repeat;

        content: '';
      }
    }
  }
</style>
